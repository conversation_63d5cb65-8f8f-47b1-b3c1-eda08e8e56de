/**
 * 全局图片缓存管理器
 * 解决跨页面图片缓存共享问题，充分利用浏览器HTTP缓存
 */

class GlobalImageCache {
  constructor() {
    // 内存缓存：存储已加载的Image对象
    this.memoryCache = new Map();
    
    // URL规范化缓存：确保相同图片使用相同URL
    this.urlCache = new Map();
    
    // 加载中的Promise缓存：避免重复请求
    this.loadingPromises = new Map();
    
    // 配置
    this.config = {
      maxMemorySize: 50, // 内存缓存最大数量
      maxUrlCacheSize: 100, // URL缓存最大数量
      enableConsoleLog: false // 是否启用调试日志
    };
    
    // 统计信息
    this.stats = {
      memoryHits: 0,
      browserCacheHits: 0,
      networkRequests: 0,
      errors: 0
    };
  }

  /**
   * 规范化图片URL，确保相同图片使用相同URL
   * @param {string} rawUrl - 原始URL
   * @returns {string} 规范化后的URL
   */
  normalizeUrl(rawUrl) {
    if (this.urlCache.has(rawUrl)) {
      return this.urlCache.get(rawUrl);
    }

    // 移除重复的斜杠
    let normalizedUrl = rawUrl.replace(/([^:]\/)\/+/g, '$1');
    
    // 确保URL以正确的协议开头
    if (normalizedUrl.startsWith('//')) {
      normalizedUrl = window.location.protocol + normalizedUrl;
    } else if (normalizedUrl.startsWith('/')) {
      normalizedUrl = window.location.origin + normalizedUrl;
    }
    
    // 缓存规范化结果
    if (this.urlCache.size >= this.config.maxUrlCacheSize) {
      const firstKey = this.urlCache.keys().next().value;
      this.urlCache.delete(firstKey);
    }
    this.urlCache.set(rawUrl, normalizedUrl);
    
    return normalizedUrl;
  }

  /**
   * 检查图片是否在浏览器缓存中
   * @param {string} url - 图片URL
   * @returns {Promise<boolean>} 是否在缓存中
   */
  async checkBrowserCache(url) {
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        cache: 'force-cache' // 强制使用缓存
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * 预加载图片，充分利用浏览器缓存
   * @param {string} url - 图片URL
   * @returns {Promise<HTMLImageElement>} 加载完成的图片对象
   */
  async preloadImage(url) {
    const normalizedUrl = this.normalizeUrl(url);
    
    // 1. 检查内存缓存
    if (this.memoryCache.has(normalizedUrl)) {
      const cachedImage = this.memoryCache.get(normalizedUrl);
      if (cachedImage.complete && cachedImage.naturalWidth > 0) {
        this.stats.memoryHits++;
        this.log('Memory cache hit:', normalizedUrl);
        return cachedImage;
      } else {
        // 移除无效缓存
        this.memoryCache.delete(normalizedUrl);
      }
    }

    // 2. 检查是否正在加载中
    if (this.loadingPromises.has(normalizedUrl)) {
      this.log('Loading in progress, waiting:', normalizedUrl);
      return await this.loadingPromises.get(normalizedUrl);
    }

    // 3. 创建加载Promise
    const loadingPromise = this.loadImageWithCache(normalizedUrl);
    this.loadingPromises.set(normalizedUrl, loadingPromise);

    try {
      const image = await loadingPromise;
      return image;
    } finally {
      // 清理加载Promise
      this.loadingPromises.delete(normalizedUrl);
    }
  }

  /**
   * 使用缓存策略加载图片
   * @param {string} url - 规范化的图片URL
   * @returns {Promise<HTMLImageElement>} 加载完成的图片对象
   */
  async loadImageWithCache(url) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      // 设置跨域属性（如果需要）
      img.crossOrigin = 'anonymous';
      
      // 监听加载完成
      img.onload = () => {
        this.stats.networkRequests++;
        this.log('Image loaded from network:', url);
        
        // 添加到内存缓存
        this.addToMemoryCache(url, img);
        
        resolve(img);
      };

      // 监听加载错误
      img.onerror = () => {
        this.stats.errors++;
        this.log('Image load error:', url);
        reject(new Error(`Failed to load image: ${url}`));
      };

      // 开始加载（浏览器会自动使用HTTP缓存）
      img.src = url;
    });
  }

  /**
   * 添加图片到内存缓存
   * @param {string} url - 图片URL
   * @param {HTMLImageElement} image - 图片对象
   */
  addToMemoryCache(url, image) {
    // 检查缓存大小限制
    if (this.memoryCache.size >= this.config.maxMemorySize) {
      // 删除最旧的缓存项
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
      this.log('Memory cache evicted:', firstKey);
    }

    this.memoryCache.set(url, image);
    this.log('Added to memory cache:', url);
  }

  /**
   * 获取缓存统计信息
   * @returns {object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      memoryCacheSize: this.memoryCache.size,
      urlCacheSize: this.urlCache.size,
      loadingPromisesSize: this.loadingPromises.size
    };
  }

  /**
   * 清理缓存
   * @param {boolean} clearMemory - 是否清理内存缓存
   * @param {boolean} clearUrl - 是否清理URL缓存
   */
  clear(clearMemory = true, clearUrl = false) {
    if (clearMemory) {
      this.memoryCache.clear();
      this.log('Memory cache cleared');
    }
    
    if (clearUrl) {
      this.urlCache.clear();
      this.log('URL cache cleared');
    }
    
    // 重置统计
    this.stats = {
      memoryHits: 0,
      browserCacheHits: 0,
      networkRequests: 0,
      errors: 0
    };
  }

  /**
   * 设置配置
   * @param {object} newConfig - 新配置
   */
  setConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 调试日志
   * @param {...any} args - 日志参数
   */
  log(...args) {
    if (this.config.enableConsoleLog) {
      console.log('[GlobalImageCache]', ...args);
    }
  }

  /**
   * 预热缓存 - 预加载常用图片
   * @param {string[]} urls - 图片URL数组
   */
  async warmup(urls) {
    this.log('Warming up cache with', urls.length, 'images');
    
    const promises = urls.map(url => 
      this.preloadImage(url).catch(error => {
        this.log('Warmup failed for:', url, error);
        return null;
      })
    );
    
    const results = await Promise.allSettled(promises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    
    this.log('Cache warmup completed:', successful, '/', urls.length, 'successful');
    return successful;
  }
}

// 创建全局单例实例
const globalImageCache = new GlobalImageCache();

// 在开发环境启用调试日志
if (process.env.NODE_ENV === 'development') {
  globalImageCache.setConfig({ enableConsoleLog: true });
}

export default globalImageCache;
