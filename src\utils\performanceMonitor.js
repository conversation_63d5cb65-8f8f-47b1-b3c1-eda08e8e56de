/**
 * 性能监控工具
 * 用于监控BOM Canvas组件的加载性能
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = new Map()
    this.isSupported = this.checkSupport()
  }

  /**
   * 检查浏览器支持情况
   */
  checkSupport() {
    return {
      performance: typeof performance !== 'undefined',
      observer: typeof PerformanceObserver !== 'undefined',
      navigation: typeof performance?.navigation !== 'undefined',
      memory: typeof performance?.memory !== 'undefined'
    }
  }

  /**
   * 开始性能测量
   * @param {string} name - 测量名称
   * @param {object} metadata - 额外的元数据
   */
  startMeasure(name, metadata = {}) {
    if (!this.isSupported.performance) return

    const startTime = performance.now()
    this.metrics.set(name, {
      startTime,
      metadata,
      status: 'running'
    })

    // 使用Performance API标记
    if (performance.mark) {
      performance.mark(`${name}-start`)
    }
  }

  /**
   * 结束性能测量
   * @param {string} name - 测量名称
   * @param {object} result - 测量结果
   */
  endMeasure(name, result = {}) {
    if (!this.isSupported.performance || !this.metrics.has(name)) return

    const endTime = performance.now()
    const metric = this.metrics.get(name)
    const duration = endTime - metric.startTime

    // 更新测量数据
    metric.endTime = endTime
    metric.duration = duration
    metric.result = result
    metric.status = 'completed'

    // 使用Performance API标记和测量
    if (performance.mark && performance.measure) {
      performance.mark(`${name}-end`)
      performance.measure(name, `${name}-start`, `${name}-end`)
    }

    return {
      name,
      duration,
      result,
      metadata: metric.metadata
    }
  }

  /**
   * 获取测量结果
   * @param {string} name - 测量名称
   */
  getMeasure(name) {
    return this.metrics.get(name)
  }

  /**
   * 获取所有测量结果
   */
  getAllMeasures() {
    return Array.from(this.metrics.entries()).map(([name, data]) => ({
      name,
      ...data
    }))
  }

  /**
   * 监控资源加载性能
   */
  observeResourceLoading() {
    if (!this.isSupported.observer) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        if (entry.initiatorType === 'img' || entry.initiatorType === 'fetch') {
          this.recordResourceMetric(entry)
        }
      })
    })

    observer.observe({ entryTypes: ['resource'] })
    this.observers.set('resource', observer)
  }

  /**
   * 记录资源加载指标
   * @param {PerformanceResourceTiming} entry - 性能条目
   */
  recordResourceMetric(entry) {
    const metric = {
      name: entry.name,
      type: entry.initiatorType,
      duration: entry.duration,
      transferSize: entry.transferSize,
      encodedBodySize: entry.encodedBodySize,
      decodedBodySize: entry.decodedBodySize,
      startTime: entry.startTime,
      responseEnd: entry.responseEnd
    }

    // 计算加载阶段
    metric.phases = {
      dns: entry.domainLookupEnd - entry.domainLookupStart,
      tcp: entry.connectEnd - entry.connectStart,
      request: entry.responseStart - entry.requestStart,
      response: entry.responseEnd - entry.responseStart
    }

    console.log('Resource loaded:', metric)
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if (!this.isSupported.memory) return null

    return {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit,
      timestamp: Date.now()
    }
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    if (!this.isSupported.observer) return

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach(entry => {
        console.warn('Long task detected:', {
          duration: entry.duration,
          startTime: entry.startTime,
          name: entry.name
        })
      })
    })

    try {
      observer.observe({ entryTypes: ['longtask'] })
      this.observers.set('longtask', observer)
    } catch (e) {
      console.warn('Long task observation not supported')
    }
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const measures = this.getAllMeasures()
    const memory = this.getMemoryUsage()
    
    const report = {
      timestamp: Date.now(),
      measures: measures.filter(m => m.status === 'completed'),
      memory,
      summary: this.generateSummary(measures)
    }

    return report
  }

  /**
   * 生成性能摘要
   * @param {Array} measures - 测量数据
   */
  generateSummary(measures) {
    const completed = measures.filter(m => m.status === 'completed')
    
    if (completed.length === 0) return null

    const durations = completed.map(m => m.duration)
    const total = durations.reduce((sum, d) => sum + d, 0)
    const average = total / durations.length
    const max = Math.max(...durations)
    const min = Math.min(...durations)

    return {
      totalMeasures: completed.length,
      totalDuration: total,
      averageDuration: average,
      maxDuration: max,
      minDuration: min
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.observers.forEach(observer => {
      observer.disconnect()
    })
    this.observers.clear()
    this.metrics.clear()
  }

  /**
   * 导出性能数据
   */
  exportData() {
    const report = this.generateReport()
    const blob = new Blob([JSON.stringify(report, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${Date.now()}.json`
    a.click()
    
    URL.revokeObjectURL(url)
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 自动开始监控
if (typeof window !== 'undefined') {
  performanceMonitor.observeResourceLoading()
  performanceMonitor.observeLongTasks()
}

export default performanceMonitor
