<template>
  <Suspense>
    <!-- 主要内容 -->
    <template #default>
      <BomCanvas 
        :params="params" 
        :target-part="targetPart"
        @loaded="handleLoaded"
        @error="handleError"
      />
    </template>
    
    <!-- 加载状态 -->
    <template #fallback>
      <div class="bom-canvas-loading">
        <div class="loading-container">
          <van-loading size="32px" vertical>
            <template #icon>
              <van-icon name="photo" size="32" />
            </template>
            {{ $t('bom.loading') }}
          </van-loading>
          
          <div class="loading-tips">
            <div class="tip-item">
              <van-icon name="info-o" size="14" />
              {{ $t('bom.loadingTips.priorityImage') }}
            </div>
            <div class="tip-item">
              <van-icon name="clock-o" size="14" />
              {{ $t('bom.loadingTips.progressiveLoad') }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </Suspense>
</template>

<script setup>
import { defineAsyncComponent, ref } from 'vue'
import { useI18n } from 'vue-i18n'

// 异步加载BomCanvas组件
const BomCanvas = defineAsyncComponent({
  loader: () => import('./BomCanvas.vue'),
  delay: 200, // 延迟200ms显示loading
  timeout: 10000, // 10秒超时
  errorComponent: {
    template: `
      <div class="bom-canvas-error">
        <van-empty 
          image="error" 
          :description="$t('bom.loadFailed')"
        >
          <van-button 
            type="primary" 
            size="small"
            @click="$emit('retry')"
          >
            {{ $t('common.retry') }}
          </van-button>
        </van-empty>
      </div>
    `
  },
  loadingComponent: {
    template: `
      <div class="bom-canvas-loading">
        <van-loading size="24px" vertical>
          {{ $t('bom.loading') }}
        </van-loading>
      </div>
    `
  }
})

const { t } = useI18n()

// Props
const props = defineProps({
  params: {
    type: Object,
    default: () => ({})
  },
  targetPart: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['loaded', 'error', 'retry'])

// 处理加载完成
const handleLoaded = (data) => {
  emit('loaded', data)
}

// 处理加载错误
const handleError = (error) => {
  console.error('BomCanvas loading error:', error)
  emit('error', error)
}
</script>

<style lang="scss" scoped>
.bom-canvas-loading {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 300px;

    .van-loading {
      color: #1976d2;
      margin-bottom: 24px;
    }

    .loading-tips {
      .tip-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;

        .van-icon {
          margin-right: 6px;
          color: #1976d2;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  // 横屏模式优化
  @media (orientation: landscape) {
    .loading-container {
      padding: 24px;
      max-width: 240px;

      .van-loading {
        margin-bottom: 16px;
      }

      .loading-tips .tip-item {
        font-size: 11px;
        margin-bottom: 6px;
      }
    }
  }
}

.bom-canvas-error {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  padding: 20px;
}
</style>
